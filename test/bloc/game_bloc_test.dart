// import 'package:dauntless/blocs/game_bloc.dart';
// import 'package:dauntless/models/games/war/warGameDef.dart';
// import 'package:test/test.dart';
// // import 'package:flutter_test/flutter_test.dart';
//
// void main() {
//   GameBloc gameBloc = GameBloc(game: AutoWarGame());
//
//   test('gameBloc instantiates', () {
//     expect(gameBloc, isNotNull);
//     expect(gameBloc.game is WarGame, true);
//     // expect(gameBloc.)
//     // expect(gameBloc.)
//   });
// }
