import 'package:common/models/player_type.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'player.freezed.dart';
part 'player.g.dart';

/// Model representing a player in a game_match
@freezed
abstract class Player with _$Player {
  /// Default constructor
  const factory Player({
    /// Unique identifier for the player
    required String id,

    /// Display name of the player
    String? name,

    /// Player ID from server (may be different from id in some cases)
    @JsonKey(name: 'playerId') String? playerId,

    /// Player type - default to human network player if not specified
    @Default(PlayerType.humanNetwork) PlayerType type,

    /// Additional player data as needed
    @Default({}) Map<String, dynamic> metadata,
  }) = _Player;

  /// Create from JSON
  factory Player.fromJson(Map<String, dynamic> json) => _$PlayerFromJson(json);
}
