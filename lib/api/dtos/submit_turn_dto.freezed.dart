// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'submit_turn_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SubmitTurnDto {
  GameMatchId get gameMatchId;
  PlayerId get playerId;
  List<TargetedAction> get move;
  int get turnNumber;
  Map<String, dynamic> get metadata;

  /// Create a copy of SubmitTurnDto
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SubmitTurnDtoCopyWith<SubmitTurnDto> get copyWith =>
      _$SubmitTurnDtoCopyWithImpl<SubmitTurnDto>(
          this as SubmitTurnDto, _$identity);

  /// Serializes this SubmitTurnDto to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SubmitTurnDto &&
            (identical(other.gameMatchId, gameMatchId) ||
                other.gameMatchId == gameMatchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            const DeepCollectionEquality().equals(other.move, move) &&
            (identical(other.turnNumber, turnNumber) ||
                other.turnNumber == turnNumber) &&
            const DeepCollectionEquality().equals(other.metadata, metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      gameMatchId,
      playerId,
      const DeepCollectionEquality().hash(move),
      turnNumber,
      const DeepCollectionEquality().hash(metadata));

  @override
  String toString() {
    return 'SubmitTurnDto(gameMatchId: $gameMatchId, playerId: $playerId, move: $move, turnNumber: $turnNumber, metadata: $metadata)';
  }
}

/// @nodoc
abstract mixin class $SubmitTurnDtoCopyWith<$Res> {
  factory $SubmitTurnDtoCopyWith(
          SubmitTurnDto value, $Res Function(SubmitTurnDto) _then) =
      _$SubmitTurnDtoCopyWithImpl;
  @useResult
  $Res call(
      {GameMatchId gameMatchId,
      PlayerId playerId,
      List<TargetedAction> move,
      int turnNumber,
      Map<String, dynamic> metadata});
}

/// @nodoc
class _$SubmitTurnDtoCopyWithImpl<$Res>
    implements $SubmitTurnDtoCopyWith<$Res> {
  _$SubmitTurnDtoCopyWithImpl(this._self, this._then);

  final SubmitTurnDto _self;
  final $Res Function(SubmitTurnDto) _then;

  /// Create a copy of SubmitTurnDto
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gameMatchId = null,
    Object? playerId = null,
    Object? move = null,
    Object? turnNumber = null,
    Object? metadata = null,
  }) {
    return _then(_self.copyWith(
      gameMatchId: null == gameMatchId
          ? _self.gameMatchId
          : gameMatchId // ignore: cast_nullable_to_non_nullable
              as GameMatchId,
      playerId: null == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as PlayerId,
      move: null == move
          ? _self.move
          : move // ignore: cast_nullable_to_non_nullable
              as List<TargetedAction>,
      turnNumber: null == turnNumber
          ? _self.turnNumber
          : turnNumber // ignore: cast_nullable_to_non_nullable
              as int,
      metadata: null == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SubmitTurnDto implements SubmitTurnDto {
  const _SubmitTurnDto(
      {required this.gameMatchId,
      required this.playerId,
      required final List<TargetedAction> move,
      required this.turnNumber,
      final Map<String, dynamic> metadata = const {}})
      : _move = move,
        _metadata = metadata;
  factory _SubmitTurnDto.fromJson(Map<String, dynamic> json) =>
      _$SubmitTurnDtoFromJson(json);

  @override
  final GameMatchId gameMatchId;
  @override
  final PlayerId playerId;
  final List<TargetedAction> _move;
  @override
  List<TargetedAction> get move {
    if (_move is EqualUnmodifiableListView) return _move;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_move);
  }

  @override
  final int turnNumber;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  /// Create a copy of SubmitTurnDto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SubmitTurnDtoCopyWith<_SubmitTurnDto> get copyWith =>
      __$SubmitTurnDtoCopyWithImpl<_SubmitTurnDto>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SubmitTurnDtoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SubmitTurnDto &&
            (identical(other.gameMatchId, gameMatchId) ||
                other.gameMatchId == gameMatchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            const DeepCollectionEquality().equals(other._move, _move) &&
            (identical(other.turnNumber, turnNumber) ||
                other.turnNumber == turnNumber) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      gameMatchId,
      playerId,
      const DeepCollectionEquality().hash(_move),
      turnNumber,
      const DeepCollectionEquality().hash(_metadata));

  @override
  String toString() {
    return 'SubmitTurnDto(gameMatchId: $gameMatchId, playerId: $playerId, move: $move, turnNumber: $turnNumber, metadata: $metadata)';
  }
}

/// @nodoc
abstract mixin class _$SubmitTurnDtoCopyWith<$Res>
    implements $SubmitTurnDtoCopyWith<$Res> {
  factory _$SubmitTurnDtoCopyWith(
          _SubmitTurnDto value, $Res Function(_SubmitTurnDto) _then) =
      __$SubmitTurnDtoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {GameMatchId gameMatchId,
      PlayerId playerId,
      List<TargetedAction> move,
      int turnNumber,
      Map<String, dynamic> metadata});
}

/// @nodoc
class __$SubmitTurnDtoCopyWithImpl<$Res>
    implements _$SubmitTurnDtoCopyWith<$Res> {
  __$SubmitTurnDtoCopyWithImpl(this._self, this._then);

  final _SubmitTurnDto _self;
  final $Res Function(_SubmitTurnDto) _then;

  /// Create a copy of SubmitTurnDto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? gameMatchId = null,
    Object? playerId = null,
    Object? move = null,
    Object? turnNumber = null,
    Object? metadata = null,
  }) {
    return _then(_SubmitTurnDto(
      gameMatchId: null == gameMatchId
          ? _self.gameMatchId
          : gameMatchId // ignore: cast_nullable_to_non_nullable
              as GameMatchId,
      playerId: null == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as PlayerId,
      move: null == move
          ? _self._move
          : move // ignore: cast_nullable_to_non_nullable
              as List<TargetedAction>,
      turnNumber: null == turnNumber
          ? _self.turnNumber
          : turnNumber // ignore: cast_nullable_to_non_nullable
              as int,
      metadata: null == metadata
          ? _self._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

// dart format on
