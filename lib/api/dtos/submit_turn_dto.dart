import 'package:dauntless/models/base/targeted_action.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:common/models/game.dart';
import 'package:dauntless/use_cases/players_use_case.dart';

part 'submit_turn_dto.freezed.dart';
part 'submit_turn_dto.g.dart';

@freezed
abstract class SubmitTurnDto with _$SubmitTurnDto {
  const factory SubmitTurnDto({
    required GameMatchId gameMatchId,
    required PlayerId playerId,
    required List<TargetedAction> move,
    required int turnNumber,
    @Default({}) Map<String, dynamic> metadata,
  }) = _SubmitTurnDto;

  factory SubmitTurnDto.fromJson(Map<String, dynamic> json) =>
      _$SubmitTurnDtoFromJson(json);
}