import 'package:dauntless/frameworks/match_save/match_save_manager.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_event.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_state.dart';
import 'package:dauntless/ui/liberator/screens/match/create/player_slots_section.dart';
import 'package:dauntless/ui/liberator/screens/match/match_details_panel.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/models/base/game_match.dart';
import 'package:common/models/match_status.dart';
import 'package:common/models/player.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Panel for creating new matches or editing existing matches with player slot configuration
class CreateMatchPanel extends StatelessWidget {
  const CreateMatchPanel({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<CreateMatchBloc, CreateMatchState>(
        builder: (context, state) {
          if (state.processingStatus == ProcessingStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          }

          // if (state.errorMessage != null) {
          //   return _ErrorState(errorMessage: state.errorMessage!);
          // }

          // Otherwise show the match creation form
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(state.toString()),
                  ListTile(
                    // title: Text(
                    //   state.serverMatchId != null
                    //       ? 'Selected Match'
                    //       : 'Create Match',
                    //   style: TextStyle(
                    //     fontWeight: FontWeight.bold,
                    //     color: state.serverMatchId != null ? Colors.blue : null,
                    //   ),
                    // ),
                    leading: Icon(Icons.gamepad_outlined,
                      color: Colors.blue,
                    ),
                    // Add banner to indicate match is selected from open matches
                    // subtitle: state.serverMatchId != null
                    //     ? const Text(
                    //         'This match was selected from open matches',
                    //         style: TextStyle(fontSize: 12))
                    //     : null,
                  ),
                  _MatchConfigurationCard(state: state),
                  const SizedBox(height: 16),
                  PlayerSlotsSection(),
                  const SizedBox(height: 24),
                  if (state.startMatchButtonEnabled) BlocBuilder<UserManager, UserState>(
                    builder: (context, userState) {
                      return ElevatedButton(
                        onPressed: () {
                          // Convert player slots to players, assigning current user to humanLocal slots
                          final currentUserId = userState.user?.id ?? '';
                          final players = _convertPlayerSlotsToPlayers(state.playerSlots, currentUserId);

                          context.read<CommandCenterBloc>().add(
                            TapStartMatchEvent(
                              gameConfig: state.selectedConfig,
                              players: players,
                            ),
                          );
                        },
                        child: const Text('Start Match'),
                      );
                    },
                  ),
                ],
              ),
            ),
          );
        },
      );
}

/// Helper function to convert player slots to players
List<Player> _convertPlayerSlotsToPlayers(List<PlayerSlot> playerSlots, String currentUserId) {
  return playerSlots.map((slot) {
    // Determine the player ID based on slot type and current assignments
    String playerId;
    if (slot.type == PlayerType.humanLocal) {
      // For humanLocal players, use the current user ID if no player is assigned
      playerId = slot.playerId?.isNotEmpty == true ? slot.playerId! : currentUserId;
    } else {
      // For other types, use the assigned player ID or generate a default
      playerId = slot.playerId ?? 'bot_${slot.id}';
    }

    return Player(
      id: playerId,
      name: slot.name ?? 'Unnamed Player',
      playerId: playerId,
      type: slot.type,
    );
  }).toList();
}

/// Error state widget
class _ErrorState extends StatelessWidget {
  final String errorMessage;

  const _ErrorState({required this.errorMessage});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Error: $errorMessage',
            style: const TextStyle(color: Colors.red),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => context.read<CreateMatchBloc>().add(
                  const InitializeCreateMatchEvent(),
                ),
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }
}

/// Match configuration card widget
class _MatchConfigurationCard extends StatelessWidget {
  final CreateMatchState state;

  const _MatchConfigurationCard({required this.state});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Match Configuration',
                style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),

            // Game type dropdown
            DropdownButtonFormField<String>(
              value: state.availableConfigs
                      .any((config) => config.id == state.selectedConfigId)
                  ? state.selectedConfigId
                  : state.availableConfigs.isNotEmpty
                      ? state.availableConfigs.first.id
                      : null,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                labelText: 'Game Type',
              ),
              items: state.availableConfigs
                  .map((config) => DropdownMenuItem(
                        value: config.id,
                        child: Text(config.name),
                      ))
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  context
                      .read<CreateMatchBloc>()
                      .add(SelectMatchConfigEvent(value));
                }
              },
            ),

            const SizedBox(height: 16),

            // Match name field
            TextFormField(
              initialValue: state.gameName,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                labelText: 'Match Name',
              ),
              onChanged: (value) {
                context.read<CreateMatchBloc>().add(UpdateGameNameEvent(value));
              },
            ),
          ],
        ),
      ),
    );
  }
}
